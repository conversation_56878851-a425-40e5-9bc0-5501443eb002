verse = "if you can keep your head when all about you are losing theirs and blaming it on you   if you can trust yourself when all men doubt you     but make allowance for their doubting too   if you can wait and not be tired by waiting      or being lied about  don’t deal in lies   or being hated  don’t give way to hating      and yet don’t look too good  nor talk too wise"
print(verse, '\n')

# TODO: replace None with appropriate code
# split verse into list of words
verse_list = verse.split()
print(verse_list, '\n')

# TODO: replace None with appropriate code
# convert list to a data structure that stores unique elements
verse_set = set(verse_list)
print(verse_set, '\n')

# TODO: replace None with appropriate code
# find the number of unique words
num_unique = len(verse_set)
print(num_unique, '\n')

### Notebook grading
correct_answer = 51
if type(verse_list) != list:
    print("`verse_list` should be a list of all words in `verse`.")
elif type(verse_set) != set:
    print("`verse_set` should be a set of all unique words in `verse_list`")
elif type(num_unique) != int:
    print("Make sure you define `num_unique` with the number of unique words!")
elif num_unique != correct_answer:
    print("Not quite! Are you finding the length of the set correctly?")
else:
    print("Nice job! You can see my solution in the next page.")

    verse_dict =  {'if': 3, 'you': 6, 'can': 3, 'keep': 1, 'your': 1, 'head': 1, 'when': 2, 'all': 2, 'about': 2, 'are': 1, 'losing': 1, 'theirs': 1, 'and': 3, 'blaming': 1, 'it': 1, 'on': 1, 'trust': 1, 'yourself': 1, 'men': 1, 'doubt': 1, 'but': 1, 'make': 1, 'allowance': 1, 'for': 1, 'their': 1, 'doubting': 1, 'too': 3, 'wait': 1, 'not': 1, 'be': 1, 'tired': 1, 'by': 1, 'waiting': 1, 'or': 2, 'being': 2, 'lied': 1, 'don\'t': 3, 'deal': 1, 'in': 1, 'lies': 1, 'hated': 1, 'give': 1, 'way': 1, 'to': 1, 'hating': 1, 'yet': 1, 'look': 1, 'good': 1, 'nor': 1, 'talk': 1, 'wise': 1}
print(verse_dict, '\n')

# find number of unique keys in the dictionary
num_keys = len(verse_dict)
print(num_keys)

# find whether 'breathe' is a key in the dictionary
contains_breathe = "breathe" in verse_dict
print(contains_breathe)

# get the first element in the sorted list of keys
first_key = sorted(verse_dict.values())
print(first_key[0])

# find the key with the highest value in the dictionary
key_with_max_value = max(verse_dict, key=verse_dict.get)
print(key_with_max_value)

# for loops
names = ["Joey Tribbiani", "Monica Geller", "Chandler Bing", "Phoebe Buffay"]

for name in names:
    name = name.lower().replace(" ", "_")

print(names)

usernames = ["Joey Tribbiani", "Monica Geller", "Chandler Bing", "Phoebe Buffay"]

for i in range(len(usernames)):
    usernames[i] = usernames[i].lower().replace(" ", "_")

print(usernames)

print(list(range(0,-5)))

# check prime
num_lists=[26, 39, 51, 53, 57, 79, 85]
for num_list in num_lists:
    factor_num = 0
    for i in range(1, num_list + 1):
        if num_list % i == 0:
            factor_num += 1

    if factor_num == 2:
        print("{} is a prime number.".format(num_list))
    elif factor_num > 2:
        for i in range(1, num_list + 1):
            if num_list % i == 0 and i != 1 and i != num_list:
                print("{} is not a prime number, because {} is a factor of {}.".format(num_list, i, num_list))

numbers = [
              [34, 63, 88, 71, 29],
              [90, 78, 51, 27, 45],
              [63, 37, 85, 46, 22],
              [51, 22, 34, 11, 18]
           ]

# def mean(num_list):
#     return sum(num_list) / len(num_list)

mean= lambda num_list: sum(num_list) / len(num_list)

# Replace the function above with a lambda expression below
averages = list(map(mean, numbers))

### Notebook grading
correct_answer = [57.0, 58.2, 50.6, 27.2]
if 'mean' in locals():
    print("❌ Your solution should not include the `mean` function.")
elif averages == correct_answer:
    print("✅ Nice work! You can view my solution on the next page.")
elif type(averages) != list:
    print("❌ Not quite! Did you convert the output of `map` to a list?")
else:
    print(f"❌ Not quite! The value of `averages` should be {correct_answer}")