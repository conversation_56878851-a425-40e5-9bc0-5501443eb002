# repeated DNA sequence
class Solution(object):
    def findRepeatedDnaSequences(self, s):
        """
        :type s: str
        :rtype: List[str]
        """
        if len(s) < 10:
            return []

        seen = set()
        repeated = set()

        # Check all 10-character substrings
        for i in range(len(s) - 9):
            substring = s[i:i+10]
            if substring in seen:
                repeated.add(substring)
            else:
                seen.add(substring)

        return list(repeated)

s = Solution()
print(s.findRepeatedDnaSequences("AAAAACCCCCAAAAACCCCCCAAAAAGGGTTT"))

# minimum size subarray sum
class Solution(object):
    def minSubArrayLen(self, target, nums):
        """
        :type target: int
        :type nums: List[int]
        :rtype: int
        """
        if not nums:
            return 0
        
        n = len(nums)
        min_len = float('inf')
        left = 0
        current_sum = 0

        for right in range(n):
            current_sum += nums[right]
            while current_sum >= target:
                min_len = min(min_len, right - left + 1)
                current_sum -= nums[left]
                left += 1

        return min_len if min_len != float('inf') else 0
    
s= Solution()
print(s.minSubArrayLen(7, [2,3,1,2,4,3]))

# contains duplicate
class Solution(object):
    def containsNearbyDuplicate(self, nums, k):
        seen = set()

        for i, num in enumerate(nums):
            if num in seen:
                return True
            seen.add(num)

            # Keep only k elements in the window
            if len(seen) > k:
                seen.remove(nums[i - k])

        return False

s=Solution()
print(s.containsNearbyDuplicate([1,2,3,1], 3))
print(s.containsNearbyDuplicate([1,0,1,1], 1))
print(s.containsNearbyDuplicate([1,2,3,1,2,3], 2))

# arithmetic slices
class Solution(object):
    def numberOfArithmeticSlices(self, nums):
        """
        :type nums: List[int]
        :rtype: int
        """
        if len(nums) < 3:
            return 0

        result = 0
        count_s = 0

        for i in range(2, len(nums)):
            if nums[i] - nums[i-1] == nums[i-1] - nums[i-2]:
                count_s += 1
                result += count_s
            else:
                count_s = 0

        return result
    
s=Solution()
print(s.numberOfArithmeticSlices([1,2,3,4]))
print(s.numberOfArithmeticSlices([1]))

