# Definition for a binary tree node.
class Node(object):
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right
# Binary tree inorder traversal
class Solution:
    def inorderTraversal(self, root: Node):
        result, stack = [], []
        current = root
        
        while current or stack:
            # Go as left as possible
            while current:
                stack.append(current)
                current = current.left
            # Process the node
            current = stack.pop()
            result.append(current.val)
            # Visit right subtree
            current = current.right
        
        return result


root = Node(1)
root.right = Node(2)
root.right.left = Node(3)

s=Solution()
print(s.inorderTraversal(root))

# min stack
class MinStack(object):

    def __init__(self):
        

    def push(self, val):
        """
        :type val: int
        :rtype: None
        """
        

    def pop(self):
        """
        :rtype: None
        """
        

    def top(self):
        """
        :rtype: int
        """
        

    def getMin(self):
        """
        :rtype: int
        """
        
