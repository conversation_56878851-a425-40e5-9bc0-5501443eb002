# Definition for a binary tree node.
class Node(object):
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right
# Binary tree inorder traversal
class Solution(object):
    def inorderTraversal(self, root: Node):
        result, stack = [], []
        current = root
        
        while current or stack:
            # Go as left as possible
            while current:
                stack.append(current)
                current = current.left
            # Process the node
            current = stack.pop()
            result.append(current.val)
            # Visit right subtree
            current = current.right
        
        return result


root = Node(1)
root.right = Node(2)
root.right.left = Node(3)

s=Solution()
print(s.inorderTraversal(root))

# min stack
class MinStack(object):

    def __init__(self):
        self.main_stack=[]
        self.min_stack=[]

    def push(self, val):
        """
        :type val: int
        :rtype: None
        """
        self.main_stack.append(val)
        if not self.min_stack:
            self.min_stack.append(val)
        else:
            self.min_stack.append(min(val,self.min_stack[-1]))

    def pop(self):
        """
        :rtype: None
        """
        self.main_stack.pop()
        self.min_stack.pop()

    def top(self):
        """
        :rtype: int
        """
        if self.main_stack:
            return self.main_stack[-1]
        return None

    def getMin(self):
        """
        :rtype: int
        """
        if self.min_stack:
            return self.min_stack[-1]
        return None

# Your MinStack object will be instantiated and called as such:
minStack = MinStack()
minStack.push(-2)
minStack.push(0)
minStack.push(-3)
print(minStack.getMin())  # Should return -3
minStack.pop()
print(minStack.top())     # Should return 0
print(minStack.getMin())  # Should return -2

# Basic calculator 2
class Solution(object):
    def calculate(self, s):
        """
        :type s: str
        :rtype: int
        """
        Stack = []
        num = 0
        sign = "+"
        s = s.replace(" ", "")  # remove spaces

        for i, ch in enumerate(s):
            if ch.isdigit():
                num = num * 10 + int(ch)
            if ch in "+-*/" or i == len(s) - 1:
                if sign == "+":
                    Stack.append(num)
                elif sign == "-":
                    Stack.append(-num)
                elif sign == "*":
                    Stack[-1] = Stack[-1] * num
                elif sign == "/":
                    Stack[-1] = int(float(Stack[-1]) / num)  # integer division
                sign = ch
                num = 0

        return sum(Stack)

s=Solution()
print(s.calculate("3+2*2"))

# Flatten binary tree to linked list
class Solution(object):
    def __init__(self):
        self.prev = None

    def flatten(self, root):
        """
        :type root: Optional[TreeNode]
        :rtype: None Do not return anything, modify root in-place instead.
        """
        if not root:
            return

        self.flatten(root.right)
        self.flatten(root.left)

        root.right = self.prev
        root.left = None
        self.prev = root

# Helper function to print the flattened tree in the required format
def print_flattened(root):
    result = []
    current = root
    while current:
        result.append(current.val)
        if current.right:  
            result.append(None)
        current = current.right
    return result

root = Node(1, Node(2, Node(3), Node(4)), Node(5, None, Node(6)))
s = Solution()
s.flatten(root)
print(print_flattened(root))
      

        