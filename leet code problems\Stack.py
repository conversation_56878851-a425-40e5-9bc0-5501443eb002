# Definition for a binary tree node.
class Node:
    def __init__(self, val):
        self.val = val
        self.left = None
        self.right = None
# change the array to tree
def insert_level_order(arr, i, n):
    if i < n:
        node = Node(arr[i])
        node.left = insert_level_order(arr, 2*i + 1, n)   # left child
        node.right = insert_level_order(arr, 2*i + 2, n)  # right child
        return node
    return None
# Binary tree in order traversal
def inorderTraversal(root):
    if root:
         inorderTraversal(root.left)
         print(root.val, end=" ")
         inorderTraversal(root.right)

# Example
arr = [1, 2, 3, 4, 5, 6, 7]
root = insert_level_order(arr, 0, len(arr))

print(inorderTraversal(root))

