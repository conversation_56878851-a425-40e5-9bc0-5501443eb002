# implement stack using queues
class MyStack(object):

    def __init__(self):
        self.queue1=[]
        self.queue2=[]

    def push(self, x):
        """
        :type x: int
        :rtype: None
        """
        self.queue1.append(x)
        self.queue2.append(self.queue1.pop(0))

    def pop(self):
        """
        :rtype: int
        """
        return self.queue2.pop(0)

    def top(self):
        """
        :rtype: int
        """
        return self.queue1.pop(0)

    def empty(self):
        """
        :rtype: bool
        """
        
