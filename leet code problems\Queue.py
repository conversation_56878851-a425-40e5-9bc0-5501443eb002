# implement stack using queues
class MyStack(object):

    def __init__(self):
        self.queue1 = []
        self.queue2 = []

    def push(self, x):
        """
        :type x: int
        :rtype: None
        """
        # Add to queue2, then move all elements from queue1 to queue2
        self.queue2.append(x)
        while self.queue1:
            self.queue2.append(self.queue1.pop(0))

        # Swap the queues so queue1 always has elements in stack order
        self.queue1, self.queue2 = self.queue2, self.queue1

    def pop(self):
        """
        :rtype: int
        """
        if self.queue1:
            return self.queue1.pop(0)
        return None

    def top(self):
        """
        :rtype: int
        """
        if self.queue1:
            return self.queue1[0]
        return None

    def empty(self):
        """
        :rtype: bool
        """
        return len(self.queue1) == 0
# Test the MyStack implementation
my_stack = MyStack()
my_stack.push(1)
my_stack.push(2)
print("Top:", my_stack.top())    # Should return 2
print("Pop:", my_stack.pop())    # Should return 2
print("Empty:", my_stack.empty()) # Should return False
print("Pop:", my_stack.pop())    # Should return 1
print("Empty:", my_stack.empty()) # Should return True
