print("Hello World")
verse = "If you can keep your head when all about you\n  Are losing theirs and blaming it on you,\nIf you can trust yourself when all men doubt you,\n  But make allowance for their doubting too;\nIf you can wait and not be tired by waiting,\n  Or being lied about, don’t deal in lies,\nOr being hated, don’t give way to hating,\n  And yet don’t look too good, nor talk too wise:"
print(len(verse))
print(verse.find("and"))
print(verse.rfind("you"))
print(verse.count("you"))
# splitting a given string in to n/k unique substring 
# def merge_the_tools(string, k):
#     # your code goes here
#     n=len(string)
#     for i in range(0,n,k):
#         substring=string[i:i+k]
#         result=""
#         for ch in substring:
#             if ch not in result:
#                 result+=ch
#         print(result) 
    

# if __name__ == '__main__':
#     string, k = input(), int(input())
#     merge_the_tools(string, k)

# arthimetic operator
def solve(meal_cost, tip_percent, tax_percent):
    # Write your code here
    tip = meal_cost * tip_percent / 100
    tax = meal_cost * tax_percent / 100
    total = meal_cost + tip + tax
    print(round(total))
if __name__ == '__main__':
    meal_cost = float(input().strip())

    tip_percent = int(input().strip())

    tax_percent = int(input().strip())

    solve(meal_cost, tip_percent, tax_percent)

# Print a numerical triangle of height N - 1
# for i in range(1,int(input())): 
#     print((10**(i)//9)*i)